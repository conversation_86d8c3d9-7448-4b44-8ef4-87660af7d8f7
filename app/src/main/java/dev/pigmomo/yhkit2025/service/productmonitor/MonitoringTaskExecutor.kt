package dev.pigmomo.yhkit2025.service.productmonitor

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.api.model.cart.CartResponse
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailResponse
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringRequestServiceUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * 监控任务执行结果
 */
data class MonitoringTaskResult(
    val success: Boolean,
    val message: String,
    val data: Any? = null,
    val changesDetected: Int = 0,
    val importantChanges: Int = 0
)

/**
 * 监控任务执行器
 * 根据监控计划类型执行对应的API请求并存储结果
 */
class MonitoringTaskExecutor(
    private val productMonitorRepository: ProductMonitorRepository,
    private val monitoringPlanRepository: MonitoringPlanRepository
) {

    companion object {
        private const val TAG = "MonitoringTaskExecutor"
    }

    /**
     * 执行监控任务
     * @param plan 监控计划
     * @return 执行结果
     */
    suspend fun executeTask(plan: MonitoringPlanEntity): MonitoringTaskResult {
        Log.d(TAG, "开始执行监控任务: ${plan.name}, 类型: ${plan.type}")

        return try {
            // 验证监控计划配置
            val (isValid, validationMessage) = MonitoringRequestServiceUtils.validateRequestServiceConfig(
                plan
            )
            if (!isValid) {
                Log.e(TAG, "监控计划配置无效: $validationMessage")
                return MonitoringTaskResult(
                    success = false,
                    message = "配置验证失败: $validationMessage"
                )
            }

            // 创建RequestService
            val requestService = MonitoringRequestServiceUtils.createRequestService(plan)

            // 根据监控类型执行对应的任务
            val result = when (plan.type) {
                MonitoringType.CART -> executeCartMonitoring(plan, requestService)
                MonitoringType.PRODUCT_DETAIL -> executeProductDetailMonitoring(
                    plan,
                    requestService
                )
            }

            // 更新监控计划的执行记录
            if (result.success) {
                monitoringPlanRepository.updateLastExecutedAt(plan.id)
                monitoringPlanRepository.incrementExecutedCount(plan.id)
                Log.d(TAG, "监控任务执行成功: ${plan.name}")
            } else {
                Log.e(TAG, "监控任务执行失败: ${plan.name}, 原因: ${result.message}")
            }

            result

        } catch (e: Exception) {
            Log.e(TAG, "执行监控任务时发生异常: ${plan.name}", e)
            MonitoringTaskResult(
                success = false,
                message = "执行异常: ${e.message}"
            )
        }
    }

    /**
     * 执行购物车监控
     * @param plan 监控计划
     * @param requestService RequestService实例
     * @return 执行结果
     */
    private suspend fun executeCartMonitoring(
        plan: MonitoringPlanEntity,
        requestService: RequestService
    ): MonitoringTaskResult {
        Log.d(TAG, "执行购物车监控: ${plan.name}")

        return try {
            // 调用API获取购物车数据
            val cartResult = withContext(Dispatchers.IO) {
                requestService.cart.getAllCart(plan.addressId)
            }

            // 处理购物车结果
            when (cartResult) {
                is RequestResult.Success -> {
                    val cartResponse = ResponseParserUtils.parseCartResponse(cartResult.data)
                    if (cartResponse != null && cartResponse.code == 0) {
                        // 处理购物车数据
                        val processResult = processCartData(plan, cartResponse)

                        MonitoringTaskResult(
                            success = true,
                            message = "购物车监控完成",
                            data = cartResponse,
                            changesDetected = processResult.first,
                            importantChanges = processResult.second
                        )
                    } else {
                        MonitoringTaskResult(
                            success = false,
                            message = "购物车API返回错误: ${cartResponse?.message ?: "未知错误"}"
                        )
                    }
                }

                is RequestResult.Error -> {
                    MonitoringTaskResult(
                        success = false,
                        message = "购物车API调用失败: ${cartResult.error.message ?: "网络错误"}"
                    )
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "购物车监控执行异常", e)
            MonitoringTaskResult(
                success = false,
                message = "购物车监控异常: ${e.message}"
            )
        }
    }

    /**
     * 执行商品详情监控
     * @param plan 监控计划
     * @param requestService RequestService实例
     * @return 执行结果
     */
    private suspend fun executeProductDetailMonitoring(
        plan: MonitoringPlanEntity,
        requestService: RequestService
    ): MonitoringTaskResult {
        Log.d(TAG, "执行商品详情监控: ${plan.name}")

        return try {
            var totalChanges = 0
            var totalImportantChanges = 0
            val results = mutableListOf<ProductDetailResponse>()

            // 遍历所有需要监控的商品
            for (productCode in plan.productIds) {
                try {
                    // 调用商品详情API
                    val result = withContext(Dispatchers.IO) {
                        requestService.product.getSkuDetail(code = productCode)
                    }

                    when (result) {
                        is RequestResult.Success -> {
                            val productDetailResponse =
                                ResponseParserUtils.parseProductDetailResponse(result.data)
                            if (productDetailResponse != null && productDetailResponse.code == 0 && productDetailResponse.data != null) {
                                results.add(productDetailResponse)

                                // 处理商品详情数据
                                val processResult =
                                    processProductDetailData(productDetailResponse.data)
                                totalChanges += processResult.first
                                totalImportantChanges += processResult.second

                                Log.d(
                                    TAG,
                                    "商品 $productCode 监控完成，检测到 ${processResult.first} 个变化"
                                )
                            } else {
                                Log.w(
                                    TAG,
                                    "商品 $productCode API返回错误: ${productDetailResponse?.message}"
                                )
                            }
                        }

                        is RequestResult.Error -> {
                            Log.w(TAG, "商品 $productCode API调用失败: ${result.error.message}")
                        }
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "监控商品 $productCode 时发生异常", e)
                }
            }

            MonitoringTaskResult(
                success = true,
                message = "商品详情监控完成，处理了 ${results.size}/${plan.productIds.size} 个商品",
                data = results,
                changesDetected = totalChanges,
                importantChanges = totalImportantChanges
            )

        } catch (e: Exception) {
            Log.e(TAG, "商品详情监控执行异常", e)
            MonitoringTaskResult(
                success = false,
                message = "商品详情监控异常: ${e.message}"
            )
        }
    }

    /**
     * 处理购物车数据
     * @param plan 监控计划
     * @param cartResponse 购物车响应数据
     * @return Pair<变化数量, 重要变化数量>
     */
    private suspend fun processCartData(
        plan: MonitoringPlanEntity,
        cartResponse: CartResponse
    ): Pair<Int, Int> {
        var totalChanges = 0
        var importantChanges = 0

        try {
            // 提取购物车中的商品 - 需要从cartlist中的cartModels中提取Product类型的数据
            val cartItems = cartResponse.data?.cartlist ?: emptyList()
            val products = mutableListOf<Product>()

            // 遍历购物车项目，提取商品信息
            for (cartItem in cartItems) {
                for (cartModel in cartItem.cartModels) {
                    if (cartModel.modelType == CartModelTypes.PRODUCT_ITEM) {
                        val wrapper = CartModelWrapper(
                            data = cartModel.data,
                            modelType = cartModel.modelType
                        )
                        wrapper.getProduct()?.let { product ->
                            products.add(product)
                        }
                    }
                }
            }

            Log.d(TAG, "处理购物车数据，共 ${products.size} 个商品")

            // 批量处理商品监控
            for (product in products) {
                try {
                    // 如果监控计划指定了特定商品ID，则只处理这些商品
                    if (plan.productIds.isNotEmpty() && !plan.productIds.contains(product.id)) {
                        continue
                    }

                    // 更新商品监控数据并记录变化
                    val changes = productMonitorRepository.updateProductAndRecordChanges(product)
                    totalChanges += changes.size

                    // 统计重要变化
                    val importantChangeCount = changes.count { it.isImportant }
                    importantChanges += importantChangeCount

                    if (changes.isNotEmpty()) {
                        Log.d(
                            TAG,
                            "商品 ${product.id} 检测到 ${changes.size} 个变化，其中 $importantChangeCount 个重要变化"
                        )
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "处理购物车商品 ${product.id} 时发生异常", e)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理购物车数据时发生异常", e)
        }

        return Pair(totalChanges, importantChanges)
    }

    /**
     * 处理商品详情数据
     * @param productDetailData 商品详情数据
     * @return Pair<变化数量, 重要变化数量>
     */
    private suspend fun processProductDetailData(
        productDetailData: ProductDetailData
    ): Pair<Int, Int> {
        return try {
            // 更新商品监控数据并记录变化
            val changes =
                productMonitorRepository.updateProductDetailAndRecordChanges(productDetailData)

            // 统计重要变化
            val importantChangeCount = changes.count { it.isImportant }

            if (changes.isNotEmpty()) {
                Log.d(
                    TAG,
                    "商品详情 ${productDetailData.id} 检测到 ${changes.size} 个变化，其中 $importantChangeCount 个重要变化"
                )
            }

            Pair(changes.size, importantChangeCount)

        } catch (e: Exception) {
            Log.e(TAG, "处理商品详情数据时发生异常", e)
            Pair(0, 0)
        }
    }

    /**
     * 批量执行监控任务
     * @param plans 监控计划列表
     * @return 执行结果列表
     */
    suspend fun executeBatchTasks(plans: List<MonitoringPlanEntity>): List<Pair<MonitoringPlanEntity, MonitoringTaskResult>> {
        Log.i(TAG, "开始批量执行 ${plans.size} 个监控任务")
        plans.forEach { plan ->
            Log.d(TAG, "准备执行任务: ${plan.name}, ID: ${plan.id}, 类型: ${plan.type}")
        }

        val results = mutableListOf<Pair<MonitoringPlanEntity, MonitoringTaskResult>>()

        for (plan in plans) {
            try {
                Log.i(TAG, "正在执行监控任务: ${plan.name}")
                val result = executeTask(plan)
                results.add(Pair(plan, result))
                Log.i(TAG, "监控任务 ${plan.name} 执行完成，结果: ${if (result.success) "成功" else "失败 - ${result.message}"}")

                // 添加延迟以避免API请求过于频繁
                delay(1000)

            } catch (e: Exception) {
                Log.e(TAG, "批量执行监控任务时发生异常: ${plan.name}", e)
                results.add(
                    Pair(
                        plan, MonitoringTaskResult(
                            success = false,
                            message = "批量执行异常: ${e.message}"
                        )
                    )
                )
            }
        }

        Log.d(
            TAG,
            "批量监控任务执行完成，成功: ${results.count { it.second.success }}/${results.size}"
        )

        return results
    }
}
